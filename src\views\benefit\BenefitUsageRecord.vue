<template>
    <div class=" back-color-common all-padding-16 display-flex flex-column height-100">
        <van-list
            v-model:loading="listLoading"
            :finished="finished"
            @load="listOnload"
        >
            <van-cell v-for="item in recordList" :key="item.id">
                <div class="display-flex top-bottom-center tb-padding-12 border-box van-cell-group" style="min-height: 2rem">
                    <div
                        class="flex-1 display-flex flex-column top-bottom-center gap-8"
                        style="align-items: flex-start"
                    >
                        <div class="display-flex top-bottom-center font-16 font-weight-500" style="color: #262626">
                            {{ item.created_at ? item.created_at : '-' }}
                            <Icon
                                v-if="!item.isExpanded"
                                class="l-margin-4"
                                icon="icon-a-Frame1171276219"
                                size="16"
                                @click="item.isExpanded = true"
                            />
                            <Icon
                                v-if="item.isExpanded"
                                class="l-margin-4"
                                icon="icon-a-Frame1000003597"
                                size="16"
                                @click="item.isExpanded = false"
                            />
                        </div>
                        <div class="display-flex space-between top-bottom-center font-16 color-black width-100" >
                            <span>{{ item.service_name }}</span>
                            <div class="display-flex top-bottom-center">
                                -{{ item.number }}
                            </div>
                        </div>
                        <van-list
                            v-if="item.isExpanded"
                            v-model:loading="Loading"
                            finished
                        >
                            <van-cell class="van-cell-detail" v-for="itemDetail in item.extraInfo" :key="itemDetail.id">
                                <div class="display-flex top-bottom-center b-margin-8">
                                    <div 
                                        class="w-46 h-46 border-radius-4 back-color-blue color-white font-14 font-weight-500 r-margin-8 border-box lr-padding-8"
                                    >
                                        {{ itemDetail.extra_info.companyName ? itemDetail.extra_info.companyName.slice(0,4) : '暂无数据'}}
                                    </div>
                                    <div class="color-two-grey font-14 flex-1" style="text-align: left">
                                        <div class="display-flex space-between">
                                            <div
                                                class="color-black font-16 b-margin-2 maxw-250 text-ellipsis text-nowrap w-200"
                                            >
                                                {{ itemDetail.extra_info.companyName ? itemDetail.extra_info.companyName : '预扣减' }}
                                            </div>
                                            <!-- <div v-if="itemDetail.isFreezed" class="font-12" style="color: #ff854c">
                                                已冻结
                                            </div> -->
                                        </div>
                                        <div class="display-flex space-between">
                                            <div>
                                                {{ item.created_at ? item.created_at : '-' }}
                                            </div>
                                            <div class="font-16 color-black">-{{ itemDetail.belong }}</div>
                                        </div>
                                    </div>
                                </div>
                            </van-cell>
                        </van-list>
                    </div>
                </div>
            </van-cell>
        </van-list>
    </div>
</template>

<script lang='ts' setup>
import { ref, onMounted, onBeforeMount, } from 'vue'
import { useRoute } from 'vue-router'
import type { IOrderUsageRecordParams } from '@/types/order'
import orderService from '@/service/orderService' 
import type { IOrderUsageRecordResponseItem } from '@/types/order'

const route = useRoute()
const Loading = ref<boolean>(true)
const listLoading = ref<boolean>(false)
const finished = ref<boolean>(false)

const list = ref<IOrderUsageRecordResponseItem[]>([])

const queryParams = ref<IOrderUsageRecordParams>({
    page: 1,
    pageSize: 20,
    serviceOrderId: '',
})

const listOnload = () => {
    setTimeout(async () => {
        const res = await search()
        // console.log(res)
        queryParams.value.page += 1
        listLoading.value = false
        if(list.value.length === res.total){
            finished.value = true
        }
    }, 1000)
}
// 定义分组数据的接口
interface GroupedData {[key: string]: {
    id: number
    created_at: string
    number: number
    service_name:string
    isExpanded: boolean
    total: number
    extraInfo: Array<{
        id: number
        created_at: string
        change_type: string
        extra_info: {companyName: string}
        belong: number
    }>;
  };
}
// 新增recordList变量
const recordList = ref<Array<{
    id: number
    created_at: string
    number: number
    service_name:string
    isExpanded: boolean
    total: number
    extraInfo: Array<{
        id: number
        change_type: string
        extra_info: {companyName: string}
        belong: number
    }>
}>>([])

const search = async () => {
    const res = await orderService.orderServiceUsagePage(queryParams.value)
    list.value.push(...res.data)
    
    // 按日期分组处理数据
    const groupedByDate: GroupedData = {}
    
    list.value.forEach(item => {
        const date = item.created_at ? item.created_at.slice(0, 10) : '-'
        
        if (!groupedByDate[date]) {
            groupedByDate[date] = {
                id:1,
                created_at: date,
                number: 0,
                service_name: '',
                isExpanded: false,
                total: 0,
                extraInfo: [],
            }
        }
        groupedByDate[date].id += 1
        groupedByDate[date].service_name = item.service.service_name || ''
        groupedByDate[date].number += (item.belong || 0)
        groupedByDate[date].total = groupedByDate[date].extraInfo.length
        groupedByDate[date].extraInfo.push({
            id: item.id,
            created_at: item.created_at || '',
            change_type: item.change_type || '',
            extra_info: item.extra_info ? JSON.parse(item.extra_info) : { companyName: '' },
            belong: item.belong || 0
        })
    })
    
    // 转换为数组形式
    recordList.value = Object.values(groupedByDate)
    console.log(recordList.value)
    return res
}

onBeforeMount(() => {
    queryParams.value.serviceOrderId = route.query.serviceOrderId as string
})
onMounted( () => {
})
</script>

<style lang="scss" scoped>
.van-cell-group{
    margin-bottom: 16px;
}

:deep(.van-cell-detail) {
    width: 8.28rem;
}
</style>
