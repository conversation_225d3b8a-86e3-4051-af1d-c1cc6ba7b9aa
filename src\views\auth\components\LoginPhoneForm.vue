<script lang="ts" setup>
import authService from '@/service/authService'
import userService from '@/service/userService'
import { phoneValidate } from '@/utils/validate'
import { showFailToast } from 'vant'
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'

const route = useRoute()
const showPaddword = ref(false)
const phone = ref('')
const code = ref('')
const logging = ref(false)
const errMsgInfo = ref('')
const countdown = ref(0) // 记录倒计时秒数
const querying = ref(false) // 控制按钮的 loading 状态
const timer = ref<ReturnType<typeof setInterval> | null>(null)
const router = useRouter()
const store = useStore()
const inviter = route.query.inviter

const validate = () => {
    let flag = true

    if (!code.value) {
        errMsgInfo.value = '短信验证码不能为空'
        flag = false
    }

    if (!phone.value) {
        errMsgInfo.value = '手机号码不能为空'
        flag = false
    }

    return flag
}

const submitForm = () => {
    if (logging.value) return
    if (!validate()) return

    const credentials = {
        smsCode: code.value,
        username: phone.value.trim(),
        grant_type: 'mobile_captcha',
        inviterCode: '',
    }

    if (inviter && typeof inviter === 'string') {
        credentials.inviterCode = inviter
    }

    logging.value = true

    authService
        .mobileOauthToken(credentials)
        .then((res) => {
            logging.value = false
            const { errCode, data, errMsg } = res || {}
            if (errCode === 0) {
                const { isNew } = data || {}
                store.dispatch('auth/loginSuccess', data)

                userService.userGetAccountInfo().then((account) => {
                    store.dispatch('user/setAccountInfo', { ...account })
                })

                if (isNew) {
                    router.push({
                        name: 'receive-award',
                    })
                } else {
                    router.push('/')
                }
            } else {
                showFailToast(errMsg || '验证码登录失败')
            }
            console.log(res)
        })
        .catch(() => {
            logging.value = false
            showFailToast('系统错误，请稍后再试')
        })
}

const onClickRight = () => {
    showPaddword.value = !showPaddword.value
    if(code.value){
        code.value = ''
    }
}

const getCode = async () => {
    if (!phone.value) {
        errMsgInfo.value = '手机号码不能为空'
        return
    }

    if (!phoneValidate(phone.value.trim())) {
        errMsgInfo.value = '手机号码格式不正确'
        return
    }

    if (countdown.value > 0 || querying.value) return // 防止重复点击
    querying.value = true
    authService
        .mobileGetLoginCode({
            mobile: phone.value.trim(),
        })
        .then(() => {
            console.log('验证码发送成功')
            // 开始倒计时
            countdown.value = 60
            querying.value = false // 取消 loading
            timer.value = setInterval(() => {
                countdown.value--
                if (countdown.value <= 0 && timer.value) {
                    clearInterval(timer.value)
                    timer.value = null
                }
            }, 1000)
        })
        .catch((err) => {
            console.log(err)
            querying.value = false
        })
}

const onBack = () => {
    router.push({ name: 'home' })
    // const previousRoute = router.options.history.state.back
    // if (previousRoute) {
    //     router.go(-1)
    // } else {
    //     router.push({ name: 'home' })
    // }
}
</script>

<template>
    <div class="login-phone-form flex flex-column lr-padding-16 gap-12 t-padding-12">
        <div class="flex flex-column gap-8">
            <div class="font-14 lh-20 color-black b-margin-8">手机号码</div>
            <div class="flex h-44 border border-radius-8 lr-padding-16 top-bottom-center">
                <van-field
                    v-model="phone"
                    class="font-14 lh-20 h-20 all-padding-0"
                    placeholder="请输入手机号码"
                    clearable
                />
            </div>
        </div>
        <div class="flex flex-column gap-8">
            <div class="font-14 lh-20 color-black">验证码</div>
            <div class="flex h-44 border border-radius-8 lr-padding-16 top-bottom-center">
                <van-field
                    v-model="code"
                    class="font-14 lh-20 h-20 all-padding-0"
                    placeholder="请输入手机验证码"
                >
                    <template #right-icon>
                        <van-icon name="clear" size="20px" color="#B3B3B3" @click="onClickRight"/>
                    </template>
                    <template #button>
                        <van-button
                            size="large"
                            type="primary"
                            class="smsBtn"
                            :disabled="countdown > 0"
                            @click="getCode"
                            :loading="querying"
                        >
                            {{ countdown > 0 ? `${countdown} 秒` : '获取验证码' }}</van-button
                        >
                    </template>
                </van-field>
            </div>
        </div>
        <div class="flex flex-row font-12 lh-16 space-between">
            <div class="color-red">{{ errMsgInfo }}</div>
            <div>首次登录即自动注册</div>
        </div>
        <van-button class="submit-btn" :loading="logging" @click="submitForm">登 录</van-button>
        <van-button class="back-btn" @click="onBack">返 回</van-button>
    </div>
</template>

<style lang="scss" scoped>
.submit-btn {
    height: 44px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    color: #ffffff;
    font-weight: 500;
    background: linear-gradient(99.4deg, #3c74eb 2.86%, #95d5f4 101.71%);
    border-radius: 8px;
    margin-top: 80px;
    border: none;
}

.smsBtn {
    font-size: 14px;
    color: var(--main-blue-);
    height: 14px;
    background-color: transparent;
    border: none;
    padding-left: 12px;
}

.login-phone-form :deep(.van-field__button) {
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.login-phone-form :deep(.van-button):before {
    background: transparent;
}

.back-btn {
    height: 44px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    color: var(--three-grey);
    border-radius: 8px;
    margin-top: 4px;
    border: none;
}

.login-phone-form {
    :deep(.van-cell) {
        padding: 0;
        line-height: unset;
    }
    :deep(.van-cell__value) {
        display: flex;
    }
    :deep(.van-field__body) {
        width: 100%;
    }
}
</style>
