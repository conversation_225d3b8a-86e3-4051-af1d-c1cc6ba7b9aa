<template>
    <div class="home tb-padding-12 lr-padding-16 border-box" style="background-color: #f4fbff">
        <div class="display-flex space-between top-bottom-center" style="width: 100%">
            <div class="display-flex">
                <van-image round width="1.2rem" height="1.2rem" :src="touxiangImg" @click="toUserInfo"/>
                <div class="display-flex flex-column l-margin-6">
                    <span v-if="user" class="font-18 font-weight-500 color-black" @click="toUserInfo">{{ userInfo.nickname }}</span>
                    <span v-else class="font-18 font-weight-500 color-black" @click="toLogin">未登录</span>
                    <span class="font-12 color-two-grey t-margin-4">
                        <Icon icon="icon-zhanghao" size="16" />
                        <span>{{ userInfo.username }}</span>
                        <span class="lr-margin-8">|</span>
                        <Icon icon="icon-zuzhi" size="16" />
                        <span>{{ userInfo.org }}</span>
                    </span>
                </div>
            </div>

            <!-- 右侧我的积分 -->
            <div class="right-button-component" @click="toMyPoints()">
                <Icon icon="icon-jifen" color="#FF854C" size="16" />
                <span class="font-14 color-black">我的积分</span>
            </div>
        </div>
        <div class="height-25 t-margin-8">
            <div class="height-25 t-margin-8">
                <div class="height-100 width-100 border-radius-8 oh">
                    <div class="swipe-container">
                        <div v-for="(image, index) in images" :key="index" class="swipe-item">
                            <div class="relative width-100 height-100" @click="toBenefit(image.type)">
                                <img :src="image.src" class="width-100 height-100 border-radius-8 img-cover" />
                                <div class="absolute display-flex flex-column" style="top: 10px; left: 10px">
                                    <div class="display-flex top-bottom-center gap-4 b-margin-4">
                                        <Icon :icon="image.icon" :color="image.color" size="16" />
                                        <span class="font-16 font-weight-500" :style="{ color: `${image.color}` }">{{
                                            image.name
                                        }}</span>
                                    </div>
                                    <span
                                        class="font-12"
                                        style="width: 5rem"
                                        :style="{ color: `${image.contentColor}` }"
                                    >
                                        {{ image.content }}
                                    </span>
                                </div>
                                <div
                                    class="absolute display-flex top-bottom-center gap-4"
                                    style="top: 10px; right: 10px"
                                >
                                    <span class="font-12 r-margin-4" :style="{ color: `${image.color}` }">去查看</span>
                                    <Icon icon="icon-a-Frame51" :color="image.color" size="16" />
                                </div>
                                <div
                                    class="absolute display-flex"
                                    style="bottom: 20px; left: 10px; align-items: flex-end"
                                >
                                    <span class="font-12" :style="{ color: `${image.contentColor}` }">可用额度：</span>
                                    <span class="font-20 font-weight-500" :style="{ color: `${image.color}` }">
                                        {{ image.amount }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="display-flex t-margin-8 gap-16" style="height: 4rem; background-color: transparent">
            <div
                class="t-padding-8 lr-padding-4 border-radius-8"
                style="width: 50%; background-color: #ffffff"
                @click="toOrders"
            >
                <div class="display-flex top-bottom-center space-between">
                    <div class="display-flex top-bottom-center">
                        <div class="flex-center" style="background-color: #3C74EB;width: 0.5rem;height: 0.5rem;border-radius: 0.25rem;">
                            <Icon icon="icon-dingdan" size="16" color="#FFFFFF"/>
                        </div>
                        <span class="font-14 color-black l-margin-4">我的订单</span>
                    </div>
                    <Icon icon="icon-a-Frame51" />
                </div>
                <div class="t-margin-4 font-12 color-two-grey">权益在手，尊享无忧</div>
                <div class="t-margin-16 display-flex left-right-center">
                    <img src="@/assets/images/my/dingdan.png" style="width: 3rem; height: 1.8rem" />
                </div>
            </div>
            <div class="t-padding-8 lr-padding-4 border-radius-8" style="width: 50%; background-color: #ffffff" @click="toHelp">
                <div class="display-flex top-bottom-center space-between">
                    <div class="display-flex top-bottom-center">
                        <div class="flex-center" style="background-color: #EB8B3C;width: 0.5rem;height: 0.5rem;border-radius: 0.25rem;">
                            <Icon icon="icon-kefu" size="16" color="#FFFFFF" />
                        </div>
                        <span class="font-14 color-black l-margin-4">帮助与客服</span>
                    </div>
                    <Icon icon="icon-a-Frame51" size="16" />
                </div>
                <div class="t-margin-4 font-12 color-two-grey">用心倾听，全程守护</div>
                <div class="t-margin-16 display-flex left-right-center">
                    <img src="@/assets/images/my/kefu.png" style="width: 3rem; height: 1.8rem" />
                </div>
            </div>
        </div>
        <div class="display-flex t-margin-16" @click="toInvite">
            <img src="@/assets/images/my/invitenew.png" style="width: 100%; height: 3.61rem" />
        </div>
        <!-- 退出登录按钮 -->
        <!-- <div
            class="flex-center all-padding-16 b-margin-4 absolute bottom-0 left-0 right-0"
            :style="{ marginBottom: paddingBottom }"
        >
            <button v-if="user" class="logout-button" @click="handleLogout">退出登录</button>
            <button v-else class="logout-button" @click="toLogin">
                去登录
            </button>
        </div> -->
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
import Icon from '@/components/common/Icon.vue'
import { useRouter } from 'vue-router'
// import { showConfirmDialog } from 'vant'
import { tabbarheight } from '@/utils/tabbar-height'
import orderService from '@/service/orderService'
import lxfsImg from '@/assets/images/my/lxfs-big.png'
import fpbgImg from '@/assets/images/my/fp-big.png'
import csbgImg from '@/assets/images/my/cs-big.png'
import touxiangImg from '@/assets/images/my/touxiang.png'

const fpbgAccont = ref('')
const xsAccont = ref('')
const swbgAccont = ref('')
const images = ref([
    {
        src: fpbgImg,
        name: '发票报告',
        icon: 'icon-Frame1',
        color: '#28C1FF',
        content: '分析企业涉票行为、交易架构，挖掘潜在风险',
        contentColor: '#53CDFF',
        amount: fpbgAccont,
        type: 'fpbg',
    },
    {
        src: lxfsImg,
        name: '联系方式',
        icon: 'icon-a-Frame11712762191',
        color: '#3C74EB',
        content: '可查看企业工作人员的电话及邮箱',
        contentColor: '#779EF1',
        amount: xsAccont,
        type: 'xs',
    },
    {
        src: csbgImg,
        name: '财税报告',
        icon: 'icon-Frame3',
        color: '#F98A35',
        content: '深入分析企业核心财务指标，直观剖析企业经营稳健度',
        contentColor: '#FBAD72',
        amount: swbgAccont,
        type: 'swbg',
    },
])

const store = useStore<RootState>()
const user = computed(() => {
    const { account } = store.state.user
    const { user } = account || {}
    return user
})

const orgInfo = computed(() => {
    const { account } = store.state.user
    const { orgs } = account || {}
    return orgs
})

const userInfo = computed(() => {
    return {
        nickname: user.value?.nickname || '未登录',
        username: user.value?.username || '-',
        org: orgInfo.value?.[0]?.name || '-',
    }
})

const router = useRouter()
const toMyPoints = () => {
    router.push('/my/points')
}

const toBenefit = (id: string) => {
    router.push({
        path: '/my/benefit',
        query: {
            type: id,
        },
    })
}

const toOrders = () => {
    router.push({
        name: 'myOrders',
    })
}

const toHelp = () => {
    router.push({
        name:'customerHelp'
    })
}

const toInvite = () => {
    router.push({
        name: 'invite-new',
    })
}

const toUserInfo = () => {
    router.push({
        path: '/my/myAccount',
    })
}

const toLogin = () => {
    router.push({
        name: 'phoneLogin',
    })
}

// const logout = async () => {
//     store.dispatch('auth/logout')
// }

// const handleLogout = () => {
//     console.log('退出登录')
//     showConfirmDialog({
//         title: '提示',
//         message: '确定退出登录吗？',
//     }).then(() => {
//         logout()
//     })
// }

const paddingBottom = computed(() => {
    return tabbarheight() + 'px'
})

onMounted(() => {
    console.log('user', user.value)
    console.log('orgInfo', orgInfo.value)
    if (user.value) {
        orderService.orderServiceStatistics({ serviceKeys: 'fpbg,xs,swbg' }).then((res) => {
            fpbgAccont.value = res[0] as string
            xsAccont.value = res[1] as string
            swbgAccont.value = res[2] as string
        })
    }
})
</script>

<style lang="scss" scoped>
.home {
    height: 100%;
    overflow: scroll;
    padding-bottom: v-bind(paddingBottom);
    display: flex;
    flex-direction: column;
}

.right-button-component {
    width: 92px;
    height: 32px;
    background-color: #ffffff;
    border-radius: 16px 0 0 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: -16px;
}

.logout-button {
    width: 8rem;
    height: 1.2rem;
    background: linear-gradient(to right, #3c74eb, #95d5f4);
    border: none;
    border-radius: 8px;
    color: white;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: opacity 0.3s ease;

    &:hover {
        opacity: 0.9;
    }

    &:active {
        opacity: 0.8;
    }
}

.swipe-container {
    display: flex;
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    -webkit-overflow-scrolling: touch;
    gap: 8px;

    &::-webkit-scrollbar {
        display: none;
    }
}

.swipe-item {
    flex: 0 0 auto;
    width: calc(100% - 24px);
    height: 100%;
    scroll-snap-align: center;
}

.image-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.image-number {
    position: absolute;
    top: 10px;
    left: 10px;
    width: 24px;
    height: 24px;
    background: linear-gradient(to right, #3c74eb, #95d5f4);
    color: white;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    font-size: 14px;
}
</style>
