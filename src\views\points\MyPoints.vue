<template>
    <div class="content">
        <div>
            <div class="font-style">
                <div class="font-20 font-weight-600 text b-margin-6">邀请好友返积分 积分兑好礼</div>
                <div class="font-12">邀请好友轻松得积分</div>
            </div>
            <div class="gift">
                <img src="@/assets/images/points/gift.png" alt="gift" />
            </div>
        </div>
        <div class="card">
            <div class="lr-padding-16">
                <div class="display-flex t-padding-20 b-margin-4 font-14 font-weight-400 " style="width: 2.2rem;">
                    <Icon icon="icon-Frame" class="r-margin-4" color="#FF854C" size="16"/>
                    <div class="display-flex flex-column gap-8">
                        <a class="color-black">我的积分</a>
                        <span class="font-weight-500 font-32 color-black">{{ myPoints ? `${myPoints}` : '0'}}</span>
                    </div>

                </div>
                <div class="red-pocket">
                    <img src="@/assets/images/points/red-pocket.png">
                </div>
                <div class="invited font-14 font-weight-400">已邀请{{ invitedNumber }}人</div>
            </div>
        </div>
        <div class="display-flex t-margin-21 gap-1" style="height: 1rem;">
            <div class="flex-center font-16 " style="width: 50%;background-color: #FFF0E8;border-radius: 0 0 0 8px;" @click="toPointsDetail">
                <Icon icon="icon-a-Frame1171276284" class="r-margin-8" color="#FF854C" size="16"/>
                积分明细
            </div>
            <div class="flex-center font-16" style="width: 50%;background-color: #FFF0E8;border-radius: 0 0 8px 0;" @click="showRule = true">
                <Icon icon="icon-a-Frame1171276283" class="r-margin-8" color="#FF854C" size="16"/>
                兑换规则
            </div>
        </div>
        <div class="width-100" style="width: 100%;">
            <div class="tb-padding-8 lr-padding-16">
                <span class="font-weight-600 font-16">
                    积分兑换权益
                </span>
            </div>
            <van-list>
                <van-cell v-for="item in exchangeList" :key="item.id" style="min-height: 2rem;">
                    <div class="display-flex space-between top-bottom-center">
                        <div class="display-flex flex-column left-right-center" style="align-items: flex-start;">
                            <div class="font-14 color-black">{{ item.name }}</div>
                            <div class="color-two-grey">所需{{ item.points }}积分</div>
                        </div>
                        <div v-if="!myPoints || myPoints < item.points" class="display-flex center border-radius-4" style="width: 2rem;height: 0.8rem; background-color: #F9F9F9;" disabled>
                            <a style="color: #999999;">积分不足</a>
                        </div>
                        <div v-else class="flex-center border-radius-4" style="width: 2rem;height: 0.8rem; background-color: #FF854C;" @click="exchangePoints(item)">
                            <a style="color: #FFFFFF;">积分兑换</a>
                        </div>
                    </div>
                </van-cell>
            </van-list>
        </div>
    </div>
    <!-- 确认购买报告 -->
    <van-overlay :show="showExchange" z-index="100">
        <div class="flex-center" style="height: 100%;">
            <div style="width: 7rem;height: 10rem;">
                <div class="exchange-dialog" :style="{ backgroundImage: `url(${backgroundImage})` }">
                    <div class="display-flex flex-column">
                        <span class="color-black font-16">是否兑换？</span>
                        <span class="color-three-grey font-12">兑换将从账户中扣除对应积分</span>
                        <span class="font-18" style="color: #FF854C;">{{ pointName }}</span>
                    </div>
                    <div class="display-flex left-right-center gap-10">
                        <van-button color="#F9F9F9" @click="showExchange = false" style="width: 3rem;height: 1rem;">
                            <a style="color: #999999;">暂不兑换</a>
                        </van-button>
                        <van-button color="#FF854C" style="width: 3rem;height: 1rem;" @click="confirmExchange(exchangeId)">
                            <a style="color: #FFFFFF;">兑 换</a>
                        </van-button>
                    </div>
                </div>
                <div class="flex-center" style="width: 100%; height: 1.3rem;">
                    <Icon icon="icon-a-Frame1171276285" @click="showExchange = false" size="20" color="rgba(255, 255, 255, 0.7)"/>
                </div>
            </div>
        </div>
    </van-overlay>
    <!-- 兑换规则 -->
    <van-overlay :show="showRule" @click="showRule = false" z-index="100">
        <div class="flex-center" style="height: 100%;">
            <div style="width: 6.8rem;height: 10rem;">
                <div class="rule-dialog">
                    <div class="speaker-icon">
                        <img src="@/assets/images/points/speaker.png" alt="speaker" style="width: 3rem;height: 3rem;" />
                    </div>
                    <div class="display-flex flex-column b-padding-8 lr-padding-8 gap-8" >
                        <span class="color-black font-18 font-weight-500 text-center">积分兑换规则</span>
                        <span class="color-two-grey font-14">1：积分目前没有过期时间，您可以随时使用</span>
                        <span class="color-two-grey font-14">2：不同商品有不同的兑换比例，兑换专区内有详细说明</span>
                        <span class="color-two-grey font-14">3：积分金科用户对话专区内的商品</span>
                        <span class="color-two-grey font-14">4：目前对兑换的积分数量和次数没有限制</span>
                        <span class="font-14" style="color: #E95133;">5：兑换后不能撤销，请仔细核对后再操作</span>
                    </div>
                </div>
                <div class="flex-center" style="width: 100%; height: 1.3rem;">
                    <Icon icon="icon-a-Frame1171276285" @click="showRule = false" size="20" color="rgba(255, 255, 255, 0.7)"/>
                </div>
            </div>
        </div>
    </van-overlay>
</template>

<script lang='ts' setup>
import { onMounted, ref, computed } from 'vue'
import Icon from '@/components/common/Icon.vue'
import { useRouter } from 'vue-router'
import orderService from '@/service/orderService'
import type { IOrderGoodsListItem } from '@/types/order'
import { showToast, closeToast, showConfirmDialog } from 'vant'
import fpBgImg from '@/assets/images/points/fp-bg.png'
import csBgImg from '@/assets/images/points/cs-bg.png'
import xsBgImg from '@/assets/images/points/xs-bg.png'

const router = useRouter()
const exchangeList = ref<IOrderGoodsListItem[]>([])
// 我的积分
const myPoints = ref<number>(0)
// 邀请人数
const invitedNumber = ref(0)
// 权益名称
const type = ref('企业发票数据综合分析报告')
const pointName = ref('企业发票数据综合分析报告')
const exchangeId = ref('')

const showExchange = ref<boolean>(false)
const showRule = ref<boolean>(false)

// 根据 type 值动态获取背景图片
const backgroundImage = computed(() => {
    const imageMap: Record<string, string> = {
        'fpbg': fpBgImg,
        'swbg': csBgImg,
        'xs': xsBgImg
    }
    return imageMap[type.value] || xsBgImg // 默认使用 xs 背景
})
const toPointsDetail = () => {
    router.push({
        path: '/my/points-detail'
    })
}

const exchangePoints = (item: IOrderGoodsListItem) => {
    if(item.name.includes('发票')){
        type.value = 'fpbg'
    }else if(item.name.includes('财税')){
        type.value = 'swbg'
    }else{
        type.value = 'xs'
    }
    pointName.value = item.name
    exchangeId.value = item.goodsId
    showExchange.value = true
    console.log(item)
}

const confirmExchange = async (id: string) => {

    showConfirmDialog({
        title: '提示',
        message: '确定兑换该产品吗？',
    }).then(() => {
        orderService.orderExchange({goodsId:id}).then(res => {
            if (res.errCode === 0) {
                showToast({
                    message: '兑换成功',
                    duration: 1000,
                })
                setTimeout(() => {
                    showExchange.value = false
                    init()
                }, 1500)
            } else {
                showToast({
                    message: res.errMsg ,
                    duration: 1000,
                })  
            }
        })
    })
}

const init = async () => {
    showToast({
        type: 'loading',
        message: '加载中...',
        forbidClick: true,
        duration: 0, 
    })
    try {
        const [pointsRes, summaryRes, goodsRes] = await Promise.all([
            orderService.orderInvitePoints(),
            orderService.orderInvitedSummary(),
            orderService.orderExchangeGoods()
        ])

        // 处理积分数据
        if (pointsRes) {
            myPoints.value = pointsRes.balance
        }

        // 处理邀请人数数据
        if (summaryRes) {
            invitedNumber.value = summaryRes.number
        }

        // 处理商品列表数据
        if (goodsRes) {
            console.log(goodsRes)
            exchangeList.value = goodsRes.data
        }
    } catch (error) {
        console.error('加载数据失败:', error)
        closeToast() 
        showToast({
            type: 'fail',
            message: '加载失败，请重试',
        })
        return 
    }
    closeToast()
}

onMounted(() => {
    init()
})
</script>

<style lang='scss' scoped>

.content {
    width: 100vw;
    min-height: 100vh;
    padding: 0 16px;
    box-sizing: border-box;
    background-image: url('@/assets/images/points/points-bg.png');
    background-size: 100% 100%;
    :deep(.van-search) {
        padding: 12px 0;
    }
    :deep(.van-search__content) {
        background-color: #fff;
    }
    .font-style {
        padding-top: 16px;
        position: relative;
        z-index: 20; 
    }

    .gift {
    position: absolute;
    top: 30px;
    left: 264px;
    z-index: 0;
    }
    img {
        width: 3.2rem;
        height: 3.2rem;
    }

    .text {
        background: linear-gradient(to right, #FF9E70, #FF6A24);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        color: transparent;
    }

}
.card{
    top: 20px;
    height:100px;
    background: linear-gradient(to bottom, #FFF9F6, #FFF0E8);
    position: relative;
    z-index: 10; 
    border-radius: 8px 8px 0 0 ;

    .invited{
        position: absolute;
        right: 6px;
        top: 58px;
        transform: translateY(-50%);
        display: flex;
        justify-content: center;
        align-items: center;
        width: 84px;
        height: 24px;
        padding-top: 6px;
        padding-bottom: 6px;
        padding-left: 12px;
        padding-right: 12px;
        background-color: #fff;
        border: 1px solid #FF854C;
        border-radius: 8px;
        color: #FF854C;
        white-space: nowrap;
        z-index: 20; 
    }

    .red-pocket{
        position: absolute;
        top: 3px; 
        right: 32px; 
    }
    img{
        width: 61px;
        height: 61px;
    }

    .vertical-line {
        width: 6px;
        background-color: #FFF7F2;
        height: 100%;
        margin: 0 16px; 
    }

}

.exchange-dialog {
    width: 100%;
    height: 320px;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    padding: 10px;
    box-sizing: border-box;
    gap: 10px;
}

.rule-dialog {
    width: 7rem;
    height: 340px;
    background: linear-gradient(to bottom, #FFD9C7, #FFF3ED);
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    padding: 10px;
    box-sizing: border-box;
    position: relative;
    border-radius: 8px;
}

.speaker-icon {
    position: absolute;
    top: -50px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
}

</style>